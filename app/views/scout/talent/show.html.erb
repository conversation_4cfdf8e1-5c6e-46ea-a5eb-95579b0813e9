<%# Wrap in chat request button controller for modal functionality %>
<div data-controller="chat-request-button" class="min-h-screen bg-stone-50">
  <div class="w-full py-8 mx-auto max-w-7xl">

    <%# Back Button %>
    <div class="mb-6">
      <%= link_to scout_talent_index_path, class: "inline-flex items-center px-4 py-2 text-sm font-medium text-stone-700 bg-white border border-stone-300 rounded-md shadow-sm hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500" do %>
        <%= phosphor_icon "arrow-left", class: "w-4 h-4 mr-2" %>
        Back to Talent
      <% end %>
    </div>

    <%# Single Comprehensive Card - Profile + Details %>
    <div class="mb-6 overflow-hidden bg-white border rounded-lg shadow-md border-stone-200">
      <div class="p-8">
        <%# Profile Header Section %>
        <div class="mb-10">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <div class="flex items-start gap-4">
                <%# Profile Picture %>
                <div class="flex-shrink-0">
                  <% if @talent_profile.user.avatar.attached? %>
                    <%= image_tag @talent_profile.user.avatar,
                        class: "w-20 h-20 rounded-full object-cover border-2 border-stone-200",
                        alt: "#{@talent_profile.user.name&.full}'s profile picture" %>
                  <% else %>
                    <div class="flex items-center justify-center w-20 h-20 p-5 text-2xl font-bold border-2 rounded-full text-stone-700 bg-stone-100 border-stone-200">
                      <%= @talent_profile.user.name&.first&.first&.upcase || "?" %>
                    </div>
                  <% end %>
                </div>

                <%# Name, availability, headline, and badges %>
                <div class="flex-1 min-w-0">
                  <%# Name and availability status on same line %>
                  <div class="flex items-center gap-3">
                    <h1 class="text-2xl font-semibold text-stone-900"><%= @talent_profile.user.name&.full %></h1>
                    <% case @talent_profile.availability_status %>
                    <% when "available" %>
                      <span class="px-2 py-1 text-xs font-medium text-green-800 bg-green-100 rounded-full">Available</span>
                    <% when "limited" %>
                      <span class="px-2 py-1 text-xs font-medium text-yellow-800 bg-yellow-100 rounded-full">Limited</span>
                    <% when "unavailable" %>
                      <span class="px-2 py-1 text-xs font-medium text-red-800 bg-red-100 rounded-full">Unavailable</span>
                    <% end %>
                  </div>

                  <%# Headline below name %>
                  <% if @talent_profile.headline.present? %>
                    <p class="max-w-2xl mt-1 text-sm leading-6 text-stone-500"><%= @talent_profile.headline %></p>
                  <% end %>

                  <%# Prominent badge display in header %>
                  <div class="mt-4">
                    <%= render 'shared/user_badges',
                        user: @talent_profile.user,
                        context: 'profile_header',
                        limit: 3 %>
                  </div>
                </div>
              </div>
            </div>

            <%# Chat Request Button in top right %>
            <div class="flex flex-col space-y-2">
              <% if Current.user.has_pending_chat_request_with?(@talent_profile.user) %>
                <button disabled class="inline-flex items-center justify-center px-6 py-2.5 text-sm font-medium text-white bg-stone-400 rounded-md cursor-not-allowed">
                  Request Sent
                  <%= phosphor_icon "check", class: "ml-2 h-4 w-4" %>
                </button>
              <% else %>
                <button type="button"
                        data-action="click->chat-request-button#openModal"
                        data-talent-user-id="<%= @talent_profile.user.id %>"
                        data-talent-name="<%= @talent_profile.user.name&.full %>"
                        class="inline-flex items-center justify-center px-6 py-2.5 text-sm font-medium text-white bg-stone-900 rounded-md shadow-sm hover:bg-stone-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500 transition-colors">
                  Request to Chat
                  <%= phosphor_icon "chat-circle", class: "ml-2 h-4 w-4" %>
                </button>
              <% end %>
            </div>
          </div>
        </div>

        <%# Enhanced Separator %>
        <div class="py-2 mt-3 border-t border-stone-200"></div>
        <%# Professional Details Section %>
        <div class="mb-12">
          <div class="mb-8">
            <h3 class="text-lg font-semibold leading-7 text-stone-900">Professional Details</h3>
            <p class="max-w-2xl mt-1 text-sm leading-6 text-stone-500">Talent's professional details.</p>
          </div>

          <dl class="space-y-6">
            <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-6">
              <dt class="text-sm font-medium leading-6 text-stone-900">About</dt>
              <dd class="mt-2 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                <%= simple_format(@talent_profile.about) if @talent_profile.about.present? %>
              </dd>
            </div>

            <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-6">
              <dt class="text-sm font-medium leading-6 text-stone-900">Looking for</dt>
              <dd class="mt-2 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                <%= @talent_profile.looking_for if @talent_profile.looking_for.present? %>
              </dd>
            </div>

            <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-6">
              <dt class="text-sm font-medium leading-6 text-stone-900">Badges</dt>
              <dd class="mt-2 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                <% if @talent_profile.user.active_badges.any? %>
                  <%= render 'shared/user_badges',
                      user: @talent_profile.user,
                      context: 'profile_section' %>
                <% else %>
                  <span class="text-stone-500">No badges assigned</span>
                <% end %>

                <%# Legacy achievement badges fallback - show if new badges are empty but old ones exist %>
                <% if (@active_badges.blank? || @active_badges.empty?) && @talent_profile.achievement_badges.present? && @talent_profile.achievement_badges.any? %>
                  <div class="pt-4 mt-4 border-t border-stone-200">
                    <p class="mb-3 text-xs text-stone-500">Legacy Achievement Badges:</p>
                    <div class="flex flex-wrap gap-2">
                      <% @talent_profile.achievement_badges.reject(&:blank?).each do |badge| %>
                        <span class="px-3 py-1 text-sm font-medium text-purple-800 bg-purple-100 rounded-full"><%= badge %></span>
                      <% end %>
                    </div>
                  </div>
                <% end %>
              </dd>
            </div>

            <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-6">
              <dt class="text-sm font-medium leading-6 text-stone-900">Type of Ghostwriter</dt>
              <dd class="mt-2 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                <% if @talent_profile.ghostwriter_type.present? && @talent_profile.ghostwriter_type.any? { |t| t.present? } %>
                  <div class="flex flex-wrap gap-2">
                    <% @talent_profile.ghostwriter_type.reject(&:blank?).each do |type| %>
                      <span class="px-3 py-1 text-sm font-medium text-indigo-800 bg-indigo-100 rounded-full"><%= type %></span>
                    <% end %>
                  </div>
                <% else %>
                  <span class="text-stone-500">Not specified</span>
                <% end %>
              </dd>
            </div>

            <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-6">
              <dt class="text-sm font-medium leading-6 text-stone-900">Skills / Preferred Content Topics</dt>
              <dd class="mt-2 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                <% if @talent_profile.skills.present? && @talent_profile.skills.any? %>
                  <div class="flex flex-wrap gap-2">
                    <% @talent_profile.skills.reject(&:blank?).each do |skill| %>
                      <span class="px-3 py-1 text-sm font-medium text-blue-800 bg-blue-100 rounded-full"><%= skill %></span>
                    <% end %>
                  </div>
                <% elsif @talent_profile.niches.present? && @talent_profile.niches.any? %>
                  <div class="flex flex-wrap gap-2">
                    <% @talent_profile.niches.reject(&:blank?).each do |niche| %>
                      <span class="px-3 py-1 text-sm font-medium text-blue-800 bg-blue-100 rounded-full"><%= niche %></span>
                    <% end %>
                  </div>
                <% else %>
                  <span class="text-stone-500">Not specified</span>
                <% end %>
              </dd>
            </div>

            <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-6">
              <dt class="text-sm font-medium leading-6 text-stone-900">Outcomes I can achieve:</dt>
              <dd class="mt-2 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                <% if @talent_profile.outcomes.present? && @talent_profile.outcomes.any? { |o| o.present? } %>
                  <div class="flex flex-wrap gap-2">
                    <% @talent_profile.outcomes.reject(&:blank?).each do |outcome| %>
                      <span class="px-3 py-1 text-sm font-medium text-green-800 bg-green-100 rounded-full"><%= outcome %></span>
                    <% end %>
                  </div>
                <% else %>
                  <span class="text-stone-500">Not specified</span>
                <% end %>
              </dd>
            </div>
          </dl>
        </div>

        <%# Enhanced Separator %>
        <div class="py-3 mt-3 border-t border-stone-200"></div>

        <%# Social Media & Platforms Section %>
        <div class="mb-12">
          <div class="mb-8">
            <h3 class="text-lg font-semibold leading-7 text-stone-900">Social Media & Platforms</h3>
            <p class="max-w-2xl mt-1 text-sm leading-6 text-stone-500">Social media profiles and platform information.</p>
          </div>

          <%# Social Media Specialty Display %>
          <% if @talent_profile.social_media_specialty.present? && @talent_profile.social_media_specialty.any? { |s| s.present? } %>
            <div class="mb-8">
              <h4 class="mb-3 text-sm font-medium leading-6 text-stone-900">Social Media Specialty</h4>
              <div class="flex flex-wrap gap-2">
                <% @talent_profile.social_media_specialty.reject(&:blank?).each do |specialty| %>
                  <%
                    # Map platform names to brand-specific styling and icons
                    platform_config = case specialty.downcase
                    when "youtube"
                      {
                        icon: "youtube-logo",
                        classes: "text-red-700 bg-red-100 border border-red-200"
                      }
                    when "linkedin"
                      {
                        icon: "linkedin-logo",
                        classes: "text-blue-700 bg-blue-100 border border-blue-200"
                      }
                    when "instagram"
                      {
                        icon: "instagram-logo",
                        classes: "text-pink-700 bg-pink-100 border border-pink-200"
                      }
                    when "twitter", "x", "twitter (x)"
                      {
                        icon: "twitter-logo",
                        classes: "text-slate-700 bg-slate-100 border border-slate-200"
                      }
                    when "threads"
                      {
                        icon: "threads-logo",
                        classes: "text-stone-700 bg-stone-100 border border-stone-200"
                      }
                    when "facebook"
                      {
                        icon: "facebook-logo",
                        classes: "text-blue-600 bg-blue-50 border border-blue-200"
                      }
                    when "tiktok"
                      {
                        icon: "tiktok-logo",
                        classes: "text-stone-900 bg-stone-100 border border-stone-300"
                      }
                    when "pinterest"
                      {
                        icon: "pinterest-logo",
                        classes: "text-red-600 bg-red-50 border border-red-200"
                      }
                    when "snapchat"
                      {
                        icon: "snapchat-logo",
                        classes: "text-yellow-600 bg-yellow-50 border border-yellow-200"
                      }
                    else
                      {
                        icon: "globe",
                        classes: "text-stone-700 bg-stone-100 border border-stone-200"
                      }
                    end
                  %>
                  <span class="inline-flex items-center px-3 py-1.5 text-sm font-medium rounded-full <%= platform_config[:classes] %>">
                    <%= phosphor_icon platform_config[:icon], class: "h-4 w-4 mr-1.5" %>
                    <%= specialty %>
                  </span>
                <% end %>
              </div>
            </div>
          <% end %>

            <div class="mb-1">
              <h4 class="mb-3 text-sm font-medium leading-6 text-stone-900">Platform Links</h4>
            </div>

          <%# Portfolio & Social Profiles Button Group %>
          <%
            # Define all available links with their properties
            available_links = [
              {
                url: @talent_profile.website_url,
                label: "Website",
                icon: "globe",
                color_classes: "text-stone-700 bg-stone-50 hover:bg-stone-100 border-stone-200 hover:border-stone-300"
              },
              {
                url: @talent_profile.portfolio_link,
                label: "Portfolio",
                icon: "folder",
                color_classes: "text-stone-700 bg-stone-50 hover:bg-stone-100 border-stone-200 hover:border-stone-300"
              },
              {
                url: @talent_profile.linkedin_url,
                label: "LinkedIn",
                icon: "linkedin-logo",
                color_classes: "text-blue-700 bg-blue-50 hover:bg-blue-100 border-blue-200 hover:border-blue-300"
              },
              {
                url: @talent_profile.x_url,
                label: "X (Twitter)",
                icon: "twitter-logo",
                color_classes: "text-slate-700 bg-slate-50 hover:bg-slate-100 border-slate-200 hover:border-slate-300"
              },
              {
                url: @talent_profile.instagram_url,
                label: "Instagram",
                icon: "instagram-logo",
                color_classes: "text-pink-700 bg-pink-50 hover:bg-pink-100 border-pink-200 hover:border-pink-300"
              },
              {
                url: @talent_profile.threads_url,
                label: "Threads",
                icon: "threads-logo",
                color_classes: "text-stone-700 bg-stone-50 hover:bg-stone-100 border-stone-200 hover:border-stone-300"
              },
              {
                url: @talent_profile.vsl_link,
                label: "Video Sales Letter",
                icon: "video",
                color_classes: "text-red-700 bg-red-50 hover:bg-red-100 border-red-200 hover:border-red-300"
              }
            ]

            # Filter to only show links that have URLs
            active_links = available_links.select { |link| link[:url].present? }
          %>

          <% if active_links.any? %>
            <div class="grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3">
              <% active_links.each do |link| %>
                <a href="<%= link[:url] %>"
                   target="_blank"
                   rel="noopener noreferrer"
                   class="inline-flex items-center justify-center px-4 py-3 text-sm font-medium border rounded-lg transition-all duration-200 <%= link[:color_classes] %>">
                  <%= phosphor_icon link[:icon], class: "h-5 w-5 mr-2" %>
                  <%= link[:label] %>
                  <%= phosphor_icon "arrow-square-out", class: "h-4 w-4 ml-2 opacity-60" %>
                </a>
              <% end %>
            </div>
          <% else %>
            <%# Empty state when no social media links are provided %>
            <div class="px-4 py-8 text-center border rounded-lg bg-stone-50 border-stone-200">
              <%= phosphor_icon "link-break", class: "h-8 w-8 text-stone-400 mx-auto mb-3" %>
              <p class="text-sm text-stone-500">No social media links or portfolio provided</p>
            </div>
          <% end %>
        </div>

        <%# Enhanced Separator %>
        <div class="py-2 mt-3 border-t border-stone-200"></div>

        <%# Pricing Details Section %>
        <div class="mb-12">
          <div class="mb-8">
            <h3 class="text-lg font-semibold leading-7 text-stone-900">Pricing Details</h3>
            <p class="max-w-2xl mt-1 text-sm leading-6 text-stone-500">Pricing information and rate details.</p>
          </div>

          <dl class="space-y-6">
            <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-6">
              <dt class="text-sm font-medium leading-6 text-stone-900">Pricing Model</dt>
              <dd class="mt-2 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                <% case @talent_profile.pricing_model %>
                <% when "hourly" %>
                  Hourly Rate
                <% when "fixed_price" %>
                  Fixed Price
                <% when "retainer" %>
                  Retainer
                <% when "project_based" %>
                  Project Based
                <% else %>
                  <span class="text-stone-500"><%= @talent_profile.pricing_model&.humanize || "Not specified" %></span>
                <% end %>
              </dd>
            </div>

            <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-6">
              <dt class="text-sm font-medium leading-6 text-stone-900">Price Range</dt>
              <dd class="mt-2 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                <% if @talent_profile.price_range_min.present? && @talent_profile.price_range_max.present? %>
                  <% if @talent_profile.pricing_model == "hourly" %>
                    $<%= number_with_precision(@talent_profile.price_range_min, precision: 0) %> - $<%= number_with_precision(@talent_profile.price_range_max, precision: 0) %> USD per hour
                  <% else %>
                    $<%= number_with_precision(@talent_profile.price_range_min, precision: 0) %> - $<%= number_with_precision(@talent_profile.price_range_max, precision: 0) %> USD
                  <% end %>
                <% else %>
                  <span class="text-stone-500">Not specified</span>
                <% end %>
              </dd>
            </div>
          </dl>
        </div>

        <%# Enhanced Separator %>
        <div class="py-2 mt-3 border-t border-stone-200"></div>

        <%# Attachments Section %>
        <div>
          <div class="mb-8">
            <h3 class="text-lg font-semibold leading-7 text-stone-900">Attachments</h3>
            <p class="max-w-2xl mt-1 text-sm leading-6 text-stone-500">Resume, cover letter, and other documents.</p>
          </div>

          <div>
            <% if @talent_profile.attachments.attached? && @talent_profile.attachments.any? %>
              <ul role="list" class="border divide-y rounded-md border-stone-200 divide-stone-200">
                <% @talent_profile.attachments.each do |attachment| %>
                  <li class="flex items-center justify-between py-4 pl-4 pr-5 text-sm leading-6">
                    <div class="flex items-center flex-1 w-0">
                      <svg class="flex-shrink-0 w-5 h-5 text-stone-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fill-rule="evenodd" d="M15.621 4.379a3 3 0 00-4.242 0l-7 7a3 3 0 004.241 4.243h.001l.497-.5a.75.75 0 011.064 1.057l-.498.501-.002.002a4.5 4.5 0 01-6.364-6.364l7-7a4.5 4.5 0 016.368 6.36l-3.455 3.553A2.625 2.625 0 119.52 9.52l3.45-3.451a.75.75 0 111.061 1.06l-3.45 3.451a1.125 1.125 0 001.587 1.595l3.454-3.553a3 3 0 000-4.242z" clip-rule="evenodd" />
                      </svg>
                      <div class="flex flex-1 min-w-0 gap-2 ml-4">
                        <span class="font-medium truncate"><%= attachment.filename %></span>
                        <span class="flex-shrink-0 text-stone-400"><%= number_to_human_size(attachment.byte_size) %></span>
                      </div>
                    </div>
                    <div class="flex-shrink-0 ml-4">
                      <%= link_to "Download", rails_blob_path(attachment, disposition: "attachment"), class: "font-medium text-blue-600 hover:text-blue-500" %>
                    </div>
                  </li>
                <% end %>
              </ul>
            <% else %>
              <div class="py-6 text-center text-stone-500">
                <p>No attachments available</p>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>

  </div>
</div>

<%# Chat Request Modal - Available globally %>
<%= render "shared/modal" %>
